{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;User Id=sa;Password=************;Database=Staging;Trusted_Connection=false;Encrypt=False"}, "TokenOptions": {"Audience": "https://api.gymkod.com", "Issuer": "https://staging.gymkod.com", "AccessTokenExpiration": 15, "RefreshTokenExpiration": 30, "SecurityKey": "STAGING_zX9c2Kf8Lm3Qp7Rt5Vw1Hy4Njt6Bd0Gs3Ae8Iu2Zy5Tx7Fq1Cm9Pk4Wv6Hn2Jl8Rd0Se3Gb7Mt_STAGING"}, "QrCodeEncryption": {"EncryptionKey": "STAGING_GymProjectQREncryptionKey2024!@#$_STAGING"}, "CorsSettings": {"AllowedOrigins": ["https://staging.gymkod.com", "http://staging.gymkod.com", "http://localhost:4200", "https://localhost:4200"], "AllowCredentials": true, "PolicyName": "StagingPolicy"}, "SecuritySettings": {"RequireHttps": true, "EnableSwagger": true}, "IpRateLimiting": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "HttpStatusCode": 429, "IpWhitelist": [], "EndpointWhitelist": ["get:/api/health", "*:/swagger/*", "post:/api/user/upload-profile-image"], "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 150}, {"Endpoint": "*", "Period": "1h", "Limit": 1500}], "SpecificRules": [{"Endpoint": "GET:/api/member/getall*", "Period": "1m", "Limit": 50}, {"Endpoint": "POST:/api/member/scannumber", "Period": "10s", "Limit": 1}, {"Endpoint": "POST:/api/auth/change-password", "Period": "5m", "Limit": 3}]}}