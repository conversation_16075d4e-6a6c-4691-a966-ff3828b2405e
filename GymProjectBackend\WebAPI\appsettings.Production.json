{"Logging": {"LogLevel": {"Default": "Error", "Microsoft.AspNetCore": "Error"}}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;User Id=sa;Password=************;Database=GymProject;Trusted_Connection=false;Encrypt=False"}, "TokenOptions": {"Audience": "https://api.gymkod.com", "Issuer": "https://admin.gymkod.com", "AccessTokenExpiration": 15, "RefreshTokenExpiration": 30, "SecurityKey": "PROD_zX9c2Kf8Lm3Qp7Rt5Vw1Hy4Njt6Bd0Gs3Ae8Iu2Zy5Tx7Fq1Cm9Pk4Wv6Hn2Jl8Rd0Se3Gb7Mt_PROD"}, "QrCodeEncryption": {"EncryptionKey": "PROD_GymProjectQREncryptionKey2024!@#$_PROD"}, "CorsSettings": {"AllowedOrigins": ["http://localhost:4200", "https://localhost:4200", "https://admin.gymkod.com", "http://admin.gymkod.com"], "AllowCredentials": true, "PolicyName": "ProductionPolicy"}, "SecuritySettings": {"RequireHttps": false, "EnableSwagger": true}, "IpRateLimiting": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "HttpStatusCode": 429, "IpWhitelist": [], "EndpointWhitelist": ["get:/api/health"], "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 200}, {"Endpoint": "*", "Period": "1h", "Limit": 2000}], "SpecificRules": [{"Endpoint": "GET:/api/member/getall*", "Period": "1m", "Limit": 30}, {"Endpoint": "GET:/api/user/getall", "Period": "1m", "Limit": 30}, {"Endpoint": "GET:/api/user/profile", "Period": "1m", "Limit": 15}, {"Endpoint": "POST:/api/member/scannumber", "Period": "10s", "Limit": 1}, {"Endpoint": "GET:/api/member/getbyphone", "Period": "10s", "Limit": 1}, {"Endpoint": "POST:/api/auth/change-password", "Period": "5m", "Limit": 5}, {"Endpoint": "GET:/api/user/remaining-profile-image-uploads", "Period": "1m", "Limit": 10}, {"Endpoint": "GET:/api/member/getmemberqrbyuserid", "Period": "3m", "Limit": 1}, {"Endpoint": "GET:/api/member/gettodayentries", "Period": "30s", "Limit": 2}]}}