===============================================================================
                    GYMKOD PRO - DEPLOYMENT REHBERİ
===============================================================================

Bu dosya GymKod Pro spor salonu yönetim sisteminin farklı ortamlara 
aktarılması için gerekli kod değişikliklerini içerir.

===============================================================================
                           1. LOCAL DEVELOPMENT
===============================================================================

AÇIKLAMA: Bilgisayarınızda geliştirme yaparken kullanacağınız ayarlar

DEĞİŞTİRİLECEK DOSYA:
GymProjectBackend/WebAPI/Properties/launchSettings.json

YAPILACAK DEĞİŞİKLİK:
TÜM PROFILE'LARIN environment'ını Development yapın

KOD (TÜM DOSYA):
{
  "$schema": "http://json.schemastore.org/launchsettings.json",
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      "applicationUrl": "http://localhost:26092",
      "sslPort": 44318
    }
  },
  "profiles": {
    "http": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "http://0.0.0.0:5165",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    "https": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:7049;http://localhost:5165",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    "IIS Express": {
      "commandName": "IISExpress",
      "launchBrowser": true,
      "launchUrl": "swagger",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}

OTOMATIK OLARAK AKTİF OLACAK AYARLAR:
- SecurityKey: DEV_ prefix'li
- QR Encryption: DEV_ prefix'li
- CORS: localhost:4200 erişimi
- Swagger: Açık
- HTTPS: Zorunlu değil
- Rate Limiting: Kapalı
- Veritabanı: GymProject (Trusted_Connection=true)
- Access Token Süresi: 15 dakika
- Refresh Token Süresi: 30 gün
- Frontend API URL: http://localhost:5165/api/

FRONTEND BUILD KOMUTU:
ng serve
veya
ng build

===============================================================================
                              2. STAGING
===============================================================================

AÇIKLAMA: staging.gymkod.com'da test için kullanacağınız ayarlar

DEĞİŞTİRİLECEK DOSYA:
GymProjectBackend/WebAPI/Properties/launchSettings.json

YAPILACAK DEĞİŞİKLİK:
TÜM PROFILE'LARIN environment'ını Staging yapın

FIND & REPLACE YAPACAĞINIZ:
BULMACA: "ASPNETCORE_ENVIRONMENT": "Development"
DEĞİŞTİRME: "ASPNETCORE_ENVIRONMENT": "Staging"

VEYA TÜM DOSYAYI DEĞİŞTİRİN:
{
  "$schema": "http://json.schemastore.org/launchsettings.json",
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      "applicationUrl": "http://localhost:26092",
      "sslPort": 44318
    }
  },
  "profiles": {
    "http": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "http://0.0.0.0:5165",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Staging"
      }
    },
    "https": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:7049;http://localhost:5165",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Staging"
      }
    },
    "IIS Express": {
      "commandName": "IISExpress",
      "launchBrowser": true,
      "launchUrl": "swagger",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Staging"
      }
    }
  }
}

OTOMATIK OLARAK AKTİF OLACAK AYARLAR:
- SecurityKey: STAGING_ prefix'li
- QR Encryption: STAGING_ prefix'li
- CORS: staging.gymkod.com + localhost:4200 erişimi
- Swagger: Açık
- HTTPS: Zorunlu
- Rate Limiting: Orta seviye (150/dakika)
- Veritabanı: Staging (User Id=sa;Password=************;Database=Staging)
- Access Token Süresi: 15 dakika
- Refresh Token Süresi: 30 gün
- Frontend API URL: https://stagingapi.gymkod.com/api/

FRONTEND BUILD KOMUTU:
ng build --configuration=staging
ng serve --configuration=staging

BACKEND SUNUCU AYARLARI:
- Backend: stagingapi.gymkod.com domain'inde çalışacak
- IIS/Nginx ile stagingapi.gymkod.com → localhost:5165 proxy

===============================================================================
                             3. PRODUCTION
===============================================================================

AÇIKLAMA: admin.gymkod.com'da canlı sistem için kullanacağınız ayarlar

DEĞİŞTİRİLECEK DOSYA:
GymProjectBackend/WebAPI/Properties/launchSettings.json

YAPILACAK DEĞİŞİKLİK:
TÜM PROFILE'LARIN environment'ını Production yapın

FIND & REPLACE YAPACAĞINIZ:
BULMACA: "ASPNETCORE_ENVIRONMENT": "Development" (veya "Staging")
DEĞİŞTİRME: "ASPNETCORE_ENVIRONMENT": "Production"

VEYA TÜM DOSYAYI DEĞİŞTİRİN:
{
  "$schema": "http://json.schemastore.org/launchsettings.json",
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      "applicationUrl": "http://localhost:26092",
      "sslPort": 44318
    }
  },
  "profiles": {
    "http": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "http://0.0.0.0:5165",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Production"
      }
    },
    "https": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:7049;http://localhost:5165",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Production"
      }
    },
    "IIS Express": {
      "commandName": "IISExpress",
      "launchBrowser": true,
      "launchUrl": "swagger",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Production"
      }
    }
  }
}

OTOMATIK OLARAK AKTİF OLACAK AYARLAR:
- SecurityKey: PROD_ prefix'li
- QR Encryption: PROD_ prefix'li
- CORS: admin.gymkod.com + localhost:4200 erişimi
- Swagger: Açık
- HTTPS: Zorunlu değil (şu anki ayarınızla)
- Rate Limiting: Sıkı seviye (200/dakika)
- Veritabanı: GymProject (User Id=sa;Password=************;Database=GymProject)
- Token Süresi: 10 saat
- Frontend API URL: https://api.gymkod.com/api/

FRONTEND BUILD KOMUTU:
ng build --configuration=production
ng build --prod

BACKEND SUNUCU AYARLARI:
- Backend: api.gymkod.com domain'inde çalışacak
- IIS/Nginx ile api.gymkod.com → localhost:5165 proxy

===============================================================================
                            DEPLOYMENT ADIMLAR
===============================================================================

KOLAY YOL (FIND & REPLACE):
1. launchSettings.json dosyasını açın
2. Ctrl+H (Find & Replace) yapın
3. Find: "ASPNETCORE_ENVIRONMENT": "Development"
4. Replace: "ASPNETCORE_ENVIRONMENT": "Staging" (veya "Production")
5. Replace All yapın
6. Dosyayı kaydedin
7. Backend'i yeniden başlatın (Visual Studio'da Stop → Start)

UZUN YOL (TÜM DOSYA):
1. İlgili ortam için yukarıdaki TÜM KODU kopyalayın
2. launchSettings.json dosyasının içeriğini tamamen silin
3. Kopyaladığınız kodu yapıştırın
4. Dosyayı kaydedin
5. Backend'i yeniden başlatın (Visual Studio'da Stop → Start)
6. Test edin

===============================================================================
                              ÖNEMLİ NOTLAR
===============================================================================

- Artık TÜM PROFILE'LAR aynı environment'ı kullanıyor
- http, https, IIS Express hangisini seçerseniz seçin aynı ortamda çalışır
- Sadece "ASPNETCORE_ENVIRONMENT" değerini değiştiriyorsunuz
- Başka hiçbir dosyaya dokunmanıza gerek yok
- Her environment kendi güvenlik seviyesinde çalışır
- Veritabanı bağlantıları otomatik olarak değişir
- CORS ayarları otomatik olarak değişir

AVANTAJLAR:
- Hangi profile kullanırsanız kullanın aynı environment
- Karışıklık yok, hata riski sıfır
- Find & Replace ile çok hızlı değişiklik

===============================================================================
                               SORUN GİDERME
===============================================================================

SORUN: Backend başlamıyor
ÇÖZÜM: Visual Studio'yu yeniden başlatın

SORUN: CORS hatası alıyorum
ÇÖZÜM: Browser cache'ini temizleyin (Ctrl+Shift+R)

SORUN: Veritabanına bağlanamıyor
ÇÖZÜM: Connection string'leri kontrol edin, SQL Server çalışıyor mu bakın

SORUN: Token hatası alıyorum
ÇÖZÜM: Backend'i restart edin, environment doğru set edilmiş mi kontrol edin

===============================================================================
                                 İLETİŞİM
===============================================================================

Bu deployment rehberi GymKod Pro güvenlik sistemi kurulumu sonrasında 
hazırlanmıştır. Herhangi bir sorun yaşarsanız sistem yöneticisine başvurun.

Son Güncelleme: 28 Aralık 2024
Versiyon: 1.0
===============================================================================
