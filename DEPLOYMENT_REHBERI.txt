===============================================================================
                    GYMKOD PRO - DEPLOYMENT REHBERİ
===============================================================================

Bu dosya GymKod Pro spor salonu yönetim sisteminin farklı ortamlara 
aktarılması için gerekli kod değişikliklerini içerir.

===============================================================================
                           1. LOCAL DEVELOPMENT
===============================================================================

AÇIKLAMA: Bilgisayarınızda geliştirme yaparken kullanacağınız ayarlar

DEĞİŞTİRİLECEK DOSYA: 
GymProjectBackend/WebAPI/Properties/launchSettings.json

YAPILACAK DEĞİŞİKLİK:
"http" profile'ındaki environment'ı Development yapın

KOD:
    "http": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "http://0.0.0.0:5165",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },

OTOMATIK OLARAK AKTİF OLACAK AYARLAR:
- SecurityKey: DEV_ prefix'li
- QR Encryption: DEV_ prefix'li  
- CORS: localhost:4200 erişimi
- Swagger: Açık
- HTTPS: Zorunlu değil
- Rate Limiting: Kapalı
- Veritabanı: GymProject (Trusted_Connection=true)
- Token Süresi: 2 saat

===============================================================================
                              2. STAGING
===============================================================================

AÇIKLAMA: staging.gymkod.com'da test için kullanacağınız ayarlar

DEĞİŞTİRİLECEK DOSYA: 
GymProjectBackend/WebAPI/Properties/launchSettings.json

YAPILACAK DEĞİŞİKLİK:
"http" profile'ındaki environment'ı Staging yapın

KOD:
    "http": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "http://0.0.0.0:5165",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Staging"
      }
    },

OTOMATIK OLARAK AKTİF OLACAK AYARLAR:
- SecurityKey: STAGING_ prefix'li
- QR Encryption: STAGING_ prefix'li
- CORS: staging.gymkod.com + localhost:4200 erişimi
- Swagger: Açık
- HTTPS: Zorunlu
- Rate Limiting: Orta seviye (150/dakika)
- Veritabanı: Staging (User Id=sa;Password=************;Database=Staging)
- Token Süresi: 5 saat

===============================================================================
                             3. PRODUCTION
===============================================================================

AÇIKLAMA: admin.gymkod.com'da canlı sistem için kullanacağınız ayarlar

DEĞİŞTİRİLECEK DOSYA: 
GymProjectBackend/WebAPI/Properties/launchSettings.json

YAPILACAK DEĞİŞİKLİK:
"http" profile'ındaki environment'ı Production yapın

KOD:
    "http": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "http://0.0.0.0:5165",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Production"
      }
    },

OTOMATIK OLARAK AKTİF OLACAK AYARLAR:
- SecurityKey: PROD_ prefix'li
- QR Encryption: PROD_ prefix'li
- CORS: admin.gymkod.com + localhost:4200 erişimi
- Swagger: Açık
- HTTPS: Zorunlu değil (şu anki ayarınızla)
- Rate Limiting: Sıkı seviye (200/dakika)
- Veritabanı: GymProject (User Id=sa;Password=************;Database=GymProject)
- Token Süresi: 10 saat

===============================================================================
                            DEPLOYMENT ADIMLAR
===============================================================================

1. İlgili ortam için yukarıdaki kodu kopyalayın
2. launchSettings.json dosyasını açın
3. "http" profile'ındaki kodu değiştirin
4. Dosyayı kaydedin
5. Backend'i yeniden başlatın (Visual Studio'da Stop → Start)
6. Test edin

===============================================================================
                              ÖNEMLİ NOTLAR
===============================================================================

- Sadece "ASPNETCORE_ENVIRONMENT" değerini değiştiriyorsunuz
- Başka hiçbir dosyaya dokunmanıza gerek yok
- Her environment kendi güvenlik seviyesinde çalışır
- Veritabanı bağlantıları otomatik olarak değişir
- CORS ayarları otomatik olarak değişir

===============================================================================
                               SORUN GİDERME
===============================================================================

SORUN: Backend başlamıyor
ÇÖZÜM: Visual Studio'yu yeniden başlatın

SORUN: CORS hatası alıyorum
ÇÖZÜM: Browser cache'ini temizleyin (Ctrl+Shift+R)

SORUN: Veritabanına bağlanamıyor
ÇÖZÜM: Connection string'leri kontrol edin, SQL Server çalışıyor mu bakın

SORUN: Token hatası alıyorum
ÇÖZÜM: Backend'i restart edin, environment doğru set edilmiş mi kontrol edin

===============================================================================
                                 İLETİŞİM
===============================================================================

Bu deployment rehberi GymKod Pro güvenlik sistemi kurulumu sonrasında 
hazırlanmıştır. Herhangi bir sorun yaşarsanız sistem yöneticisine başvurun.

Son Güncelleme: 28 Aralık 2024
Versiyon: 1.0
===============================================================================
