{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=GymProject;Trusted_Connection=true;Encrypt=False"}, "TokenOptions": {"Audience": "https://api.gymkod.com", "Issuer": "https://admin.gymkod.com", "AccessTokenExpiration": 2, "RefreshTokenExpiration": 30, "SecurityKey": "DEV_zX9c2Kf8Lm3Qp7Rt5Vw1Hy4Njt6Bd0Gs3Ae8Iu2Zy5Tx7Fq1Cm9Pk4Wv6Hn2Jl8Rd0Se3Gb7Mt_DEV"}, "QrCodeEncryption": {"EncryptionKey": "DEV_GymProjectQREncryptionKey2024!@#$_DEV"}, "CorsSettings": {"AllowedOrigins": ["http://localhost:4200", "https://localhost:4200", "http://127.0.0.1:4200", "https://127.0.0.1:4200", "http://localhost:3000", "https://localhost:3000"], "AllowCredentials": true, "PolicyName": "DevelopmentPolicy"}, "SecuritySettings": {"RequireHttps": false, "EnableSwagger": true}, "IpRateLimiting": {"EnableEndpointRateLimiting": false, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "HttpStatusCode": 429, "IpWhitelist": [], "EndpointWhitelist": ["*"], "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 1000}]}}